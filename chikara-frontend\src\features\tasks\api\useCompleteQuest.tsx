import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export function useCompleteQuest() {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.quest.complete.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: orpc.quest.getCompleted.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: orpc.quest.getActive.key(),
                });
                queryClient.invalidateQueries({ queryKey: APIROUTES.USER.CURRENTUSERINFO });
            },
            onError: (error) => {
                console.error("Failed to complete quest:", error);
            },
        })
    );
}
