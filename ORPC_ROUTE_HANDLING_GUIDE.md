# oRPC Route Handling Guide

This document explains how oRPC route handling works in the Chikara Academy codebase, covering both backend and frontend implementation patterns.

## Overview

oRPC provides type-safe RPC communication between the backend and frontend using:

- **Backend**: oRPC server with middleware-based authentication and validation
- **Frontend**: oRPC client with TanStack Query integration for caching and state management

## Backend Implementation

### 1. Core oRPC Setup (`chikara-backend/src/lib/orpc.ts`)

The core oRPC configuration provides:

#### Route Context Creation

```typescript
export async function createContext(opts: { req: { headers: IncomingHttpHeaders } }) {
    const res = await auth.api.getSession({
        headers: fromNodeHeaders(opts.req.headers),
    });

    if (!res) {
        return { session: null, user: null };
    }

    const fetchedUser = await GetUserByIdWithAssociations(Number.parseInt(res.user.id));
    return { session: res.session, user: fetchedUser! };
}
```

#### Authentication Middleware

Four levels of authentication are provided:

- **`publicAuth`**: No authentication required
- **`isLoggedInAuth`**: Requires valid user session
- **`canMakeStateChangesAuth`**: Requires authentication + user state validation (not banned, jailed, hospitalized, in battle, or on mission)
- **`adminAuth`**: Requires admin privileges

#### Error Handling

All thrown errors are automatically logged by the oRPC error interceptor:

```typescript
export const orpcLogError = (error: unknown) => {
    const isOperationalError =
        error instanceof ORPCError &&
        [
            "BAD_REQUEST",
            "UNAUTHORIZED",
            "FORBIDDEN",
            "NOT_FOUND",
            "CONFLICT",
            "UNPROCESSABLE_CONTENT",
            "TOO_MANY_REQUESTS",
        ].includes(error.code);

    if (isOperationalError) {
        logger.operational({ code: error.code, message: error.message });
    } else {
        LogErrorStack({ error: errorObj });
    }
};
```

### 2. Route Definition Pattern

Routes are defined in feature-specific files (e.g., `explore.routes.ts`):

```typescript
export const exploreRouter = {
    // Query endpoint (read-only)
    getMapByLocation: isLoggedInAuth.handler(async ({ context }) => {
        const { id, currentMapLocation } = context.user;
        const response = await ExploreController.getExploreMapByLocation(id, currentMapLocation);
        return handleResponse(response);
    }),

    // Mutation endpoint with input validation
    interactWithNode: canMakeStateChangesAuth
        .input(exploreSchema.interactWithNode)
        .handler(async ({ input, context }) => {
            const response = await ExploreController.interactWithNode(context.user.id, input.nodeId, input.isStatic);
            return handleResponse(response);
        }),
};
```

### 3. Input Validation (`explore.validation.ts`)

Zod schemas define and validate input types:

```typescript
export const InteractWithNodeSchema = z.object({
    nodeId: z.number().int().positive(),
    isStatic: z.boolean(),
});

const exploreSchema = {
    interactWithNode: InteractWithNodeSchema,
    completeNode: CompleteNodeSchema,
    // ... other schemas
};
```

### 4. Router Registration (`orpcRouter.ts`)

All feature routers are combined into a single app router:

```typescript
export const appRouter = {
    healthCheck: publicAuth.handler(() => "OK"),
    explore: exploreRouter,
    bank: bankRouter,
    job: jobRouter,
    user: userRouter,
    // ... other routers
};

export type AppRouterClient = RouterClient<typeof appRouter>;
```

### 5. Response Handling (`utils/routeHandler.ts`)

The `handleResponse` function converts controller results to oRPC responses:

```typescript
export function handleResponse<T>(result: ControllerResult<T>): T {
    if (result.error) {
        const errorType = getErrorType(result.statusCode);
        throw new ORPCError(errorType, { message: result.error });
    }
    return result.data as T;
}
```

## Frontend Implementation

- Any useQuery or useMutation calls should be created as separate hook files in the relevant feature/api directory

### 1. oRPC Client Location (`chikara-frontend/src/lib/orpc.ts`)

```typescript
const baseURL = import.meta.env.VITE_API_BASE_URL || "http://localhost:3000";

export const link = new RPCLink({
    url: `${baseURL}/rpc`,
    headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
    },
    fetch(url, options) {
        return fetch(url, {
            ...options,
            credentials: "include", // Include cookies for authentication
        });
    },
});

export const client: AppRouterClient = createORPCClient(link);
export const orpc = createTanstackQueryUtils(client);
```

### 2. Query Hooks Pattern

For read operations, use `useQuery` with oRPC query options:

```typescript
const useGetActiveQuestList = (options: QueryOptions = {}) => {
    return useQuery(
        orpc.quest.getActive.queryOptions({
            input: { questId: 1 },
            staleTime: 60000,
            ...options,
        })
    );
};
```

- REMEMBER: Query options should be added as an argument to the `queryOptions` function, not as a second argument to `useQuery`.
- For useQuery all inputs should be provided as an object with an `input` property in queryOptions

### 3. Mutation Hooks Pattern

For write operations, use `useMutation` with oRPC mutation options:

```typescript
const useExploreInteract = (options?: UseExploreInteractOptions) => {
    const queryClient = useQueryClient();
    const navigate = useNavigate();

    return useMutation(
        orpc.explore.interactWithNode.mutationOptions({
            onSuccess: (data) => {
                if (!data.success) return;

                // Handle different response types
                if (data.action === "battle") {
                    navigate(`/fight`);
                } else if (data.action === "story") {
                    queryClient.invalidateQueries({ queryKey: orpc.story.key() });
                }

                // Invalidate related queries
                queryClient.invalidateQueries({
                    queryKey: orpc.explore.getMapByLocation.key(),
                });
            },
            onError: (error) => {
                console.error("Node interaction error:", error);
            },
        })
    );
};
```

- Mutation options should be added as an argument object to the `mutationOptions` fn
- oRPC mutations expect the input to be passed directly as the argument, not wrapped in an input object.

### 4. File Handling

- oRPC natively supports file uploads and downloads using standard File and Blob objects, requiring no additional configuration.
- To validate file uploads and downloads, you can use the z.instanceof(File) and z.instanceof(Blob) validators.

```typescript
const example = os
    .input(z.object({ file: z.instanceof(File) }))
    .output(z.object({ file: z.instanceof(File) }))
    .handler(async ({ input }) => {
        console.log(input.file.name);
        return { file: input.file };
    });
```

## Key Patterns and Best Practices

### 1. Authentication Levels

- Use `publicAuth` for public endpoints
- Use `isLoggedInAuth` for read operations requiring authentication
- Use `canMakeStateChangesAuth` for write operations
- Use `adminAuth` for admin-only operations

### 2. Input Validation

- Always define Zod schemas for inputs
- Use `.input(schema)` to attach validation to routes
- Keep validation schemas in separate files

### 3. Error Handling

- Use `handleResponse()` to convert controller results
- Throw `ORPCError` for expected errors
- Let unexpected errors bubble up for logging

### 4. Frontend Query Management

- Use `orpc.feature.method.queryOptions()` for queries
- Use `orpc.feature.method.mutationOptions()` for mutations
- Invalidate related queries after successful mutations
- Use `orpc.feature.key()` for broad invalidation

### 5. Type Safety

- Import `AppRouterClient` type from backend
- All routes are fully type-safe from backend to frontend
- Input and output types are automatically inferred

### 6. Query Key Management

- Use `orpc.feature.method.key()` for specific query keys
- Use `orpc.feature.key()` for feature-level invalidation
- Combine with legacy `APIROUTES` keys when needed

This pattern provides end-to-end type safety, automatic validation, consistent error handling, and seamless integration with TanStack Query for optimal caching and state management.

## Migration Checklist

The following is a checklist of route files that still need to be converted from Express endpoints to oRPC:

- [x] actionlog.routes.ts
- [x] admin.routes.ts
- [x] auth.routes.ts
- [x] bounty.routes.ts
- [x] casino.routes.ts
- [x] chat.routes.ts
- [x] creature.routes.ts
- [x] dailyquest.routes.ts
- [x] dev.routes.ts
- [x] infirmary.routes.ts
- [x] item.routes.js
- [x] jail.routes.ts
- [x] leaderboard.routes.ts
- [x] social.routes.ts
- [x] dropchance.routes.ts
- [x] skills.routes.ts

## Migration Steps

For each feature that needs conversion:

1. Create a new `feature-name.routes.ts` file
2. Move routes from Express to oRPC format following the patterns in this guide
3. Update the `routes.ts` file to include the new router
4. Test thoroughly before removing the old Express routes
5. Update the `apiRoutes.ts` file in the frontend to use oRPC keys
6. Update frontend code to use the new oRPC endpoints
