import { isLoggedInAuth } from "../../lib/orpc.js";
import * as LeaderboardController from "./leaderboard.controller.js";

export const leaderboardRouter = {
    getLeaderBoards: isLoggedInAuth.handler(async () => {
        return await LeaderboardController.getLeaderBoards();
    }),

    getChatEmoteLeaderboards: isLoggedInAuth.handler(async () => {
        return await LeaderboardController.getChatEmoteLeaderboards();
    }),
};

export default leaderboardRouter;
