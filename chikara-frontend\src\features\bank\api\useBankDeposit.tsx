import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useBankDeposit = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.bank.deposit.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.BANK.BANKTRANSACTIONS,
                });
            },
        })
    );
};
