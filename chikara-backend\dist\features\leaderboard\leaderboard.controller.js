import * as Chat<PERSON>el<PERSON> from "../chat/chat.helpers.js";
import LeaderboardRepository from "../../repositories/leaderboard.repository.js";
import { createCache } from "../../utils/cacheHelper.js";
import { logger } from "../../utils/log.js";
import { leaderboardsConfig } from "../../config/gameConfig.js";
const leaderboardCache = createCache("2h");
const { USERS_PER_BOARD } = leaderboardsConfig.public;
export const getLeaderBoards = async () => {
    const cachedData = leaderboardCache.get();
    if (leaderboardCache.isValid() && cachedData) {
        return cachedData;
    }
    const [level, pvpwins, money, hench, defensive, intelligent, dexterous, endurance, vitality, zones, joblevel, crafts, npcwins, quests, muggingGain, muggingLoss, casinoWinner, casinoLoser, totalMissionHours, totalStats, totalBountyRewards, marketItemsSold, marketMoneyMade,] = await Promise.all([
        LeaderboardRepository.getLevelLeaderboard(),
        LeaderboardRepository.getPvpWinsLeaderboard(),
        LeaderboardRepository.getMoneyLeaderboard(),
        LeaderboardRepository.getStrengthLeaderboard(),
        LeaderboardRepository.getDefenceLeaderboard(),
        LeaderboardRepository.getIntelligenceLeaderboard(),
        LeaderboardRepository.getDexterityLeaderboard(),
        LeaderboardRepository.getEnduranceLeaderboard(),
        LeaderboardRepository.getVitalityLeaderboard(),
        LeaderboardRepository.getZonesLeaderboard(),
        LeaderboardRepository.getJobLevelLeaderboard(),
        LeaderboardRepository.getCraftsLeaderboard(),
        LeaderboardRepository.getNpcWinsLeaderboard(),
        LeaderboardRepository.getQuestsLeaderboard(),
        LeaderboardRepository.getMuggingGainLeaderboard(),
        LeaderboardRepository.getMuggingLossLeaderboard(),
        LeaderboardRepository.getCasinoWinnerLeaderboard(),
        LeaderboardRepository.getCasinoLoserLeaderboard(),
        LeaderboardRepository.getTotalMissionHoursLeaderboard(),
        LeaderboardRepository.getTotalStatsLeaderboard(),
        LeaderboardRepository.getTotalBountyRewardsLeaderboard(),
        LeaderboardRepository.getMarketItemsSoldLeaderboard(),
        LeaderboardRepository.getMarketMoneyMadeLeaderboard(),
    ]);
    const processedTotalStats = totalStats
        .map((user) => ({
        ...user,
        totalStatsSum: user.strength + user.defence + user.dexterity + user.intelligence + user.endurance + user.vitality,
    }))
        .sort((a, b) => b.totalStatsSum - a.totalStatsSum)
        .slice(0, USERS_PER_BOARD)
        .map(({ id, username, avatar }) => ({ id, username, avatar }));
    const leaderboardData = {
        level,
        pvpwins,
        money,
        hench,
        defensive,
        zones,
        joblevel,
        crafts,
        npcwins,
        quests,
        intelligent,
        dexterous,
        endurance,
        vitality,
        muggingGain,
        muggingLoss,
        casinoWinner,
        casinoLoser,
        totalMissionHours,
        totalStats: processedTotalStats,
        totalBountyRewards,
        marketItemsSold,
        marketMoneyMade,
    };
    const response = { data: leaderboardData, lastFetch: Date.now() };
    leaderboardCache.set(response);
    logger.info("Leaderboard cache updated");
    return response;
};
const chatEmoteLeaderboardCache = createCache("2h");
export const getChatEmoteLeaderboards = async () => {
    if (chatEmoteLeaderboardCache.isValid()) {
        const cachedData = chatEmoteLeaderboardCache.get();
        if (cachedData) {
            return cachedData;
        }
    }
    const emoteRanking = await ChatHelper.GetEmoteUsageRanking();
    const response = { data: emoteRanking };
    chatEmoteLeaderboardCache.set(response);
    logger.info("Chat emote leaderboard cache updated");
    return response;
};
