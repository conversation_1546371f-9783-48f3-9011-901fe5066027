import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

export interface BailUserRequest {
    targetId: number;
}

/**
 * Hook to bail a user out of jail
 */
export const useBailUser = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.jail.bail.mutationOptions({
            onSuccess: () => {
                toast.success("Bailed out user successfully!");
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.JAIL.JAILLIST,
                });

                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );
};

export default useBailUser;
