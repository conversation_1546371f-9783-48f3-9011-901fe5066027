import { APIROUTES } from "@/helpers/apiRoutes";
import { useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";
import { Link } from "react-router-dom";

export default function DeathPanel() {
    const queryClient = useQueryClient();

    useEffect(() => {
        queryClient.invalidateQueries({
            queryKey: APIROUTES.USER.CURRENTUSERINFO,
        });
        queryClient.invalidateQueries({
            queryKey: APIROUTES.INFIRMARY.INFIRMARYLIST,
        });
    }, []);

    return (
        <div className="flex flex-col text-gray-200">
            <p className="mx-auto my-1 text-red-600 text-xl">Defeat!</p>
            <p className="mx-auto mt-1 mb-4 text-center">You were knocked unconscious and dragged to the hospital...</p>
            <Link to="/hospital">
                <button className="mx-auto mb-2 flex h-12 w-1/2 items-center justify-center rounded-md border border-gray-700 bg-blue-700 px-4 py-2 font-medium text-gray-200 text-sm text-stroke-sm shadow-xs hover:bg-blue-800 md:mt-6">
                    To Hospital
                </button>
            </Link>
        </div>
    );
}
