import { z } from "zod";
import { NotificationTypes } from "../../types/notification.js";
export const sendNotificationSchema = z.object({
    type: z.nativeEnum(NotificationTypes),
    details: z.string().min(1, "Details are required"),
});
export const addXpSchema = z.object({
    xp: z.number().int().positive("XP must be a positive integer"),
});
export const addCashSchema = z.object({
    cash: z.number().int().positive("Cash must be a positive integer").optional().default(5000),
});
export const addStatsSchema = z.object({
    amount: z.number().int().positive("Amount must be a positive integer").optional().default(200),
});
export const removeStatsSchema = z.object({
    amount: z.number().int().positive("Amount must be a positive integer").optional().default(200),
});
export const addItemSchema = z.object({
    itemId: z.number().int().positive("Item ID must be a positive integer"),
    quantity: z.number().int().positive("Quantity must be a positive integer").optional().default(1),
});
export const removeAllEffectsSchema = z.object({
    userId: z.number().int().positive("User ID must be a positive integer").optional(),
});
export const addRandomEffectsSchema = z.object({
    userId: z.number().int().positive("User ID must be a positive integer").optional(),
});
export const addPetXpSchema = z.object({
    xp: z.number().int().positive("XP must be a positive integer").optional().default(100),
});
export const randomRoguelikeSchema = z.object({
    level: z.number().int().positive("Level must be a positive integer").optional(),
    location: z.string().optional(),
});
const devSchema = {
    sendNotification: sendNotificationSchema,
    addXp: addXpSchema,
    addCash: addCashSchema,
    addStats: addStatsSchema,
    removeStats: removeStatsSchema,
    addItem: addItemSchema,
    removeAllEffects: removeAllEffectsSchema,
    addRandomEffects: addRandomEffectsSchema,
    addPetXp: addPetXpSchema,
    randomRoguelike: randomRoguelikeSchema,
};
export default devSchema;
