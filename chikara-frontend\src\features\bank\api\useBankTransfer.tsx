import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useBankTransfer = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.bank.transfer.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.BANK.BANKTRANSACTIONS,
                });
            },
        })
    );
};
