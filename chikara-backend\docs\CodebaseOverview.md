# Chikara Academy Backend: Rules & Overview

## Main Tech Stack

- **Runtime:** NodeJS
- **Language:** TypeScript
- **Web Framework:** Express.js w/ oRPC for type-safe E2E endpoints
- **Database:** MySQL (via Prisma ORM)
- **Caching:** Redis
- **Job Queue:** BullMQ
- **Testing:** Vitest
- **Logging:** <PERSON> (custom `logger` utility)
- **Realtime**: Socket.IO

## Project Structure (Modular, Feature-Based)

```
chikara-backend/
├── prisma/ # Prisma ORM
├── public/ # Static assets
├── src/
│ ├── config/ # Configuration
│ ├── data/ # Static data
│ ├── features/ # Feature modules (See below)
│ ├── lib/ # Shared libs
│ ├── middleware/ # Express middleware
│ ├── queues/ # BullMQ queues and jobs
│ ├── core/ # Shared services
│ ├── __tests__/ # Unit test mocks
│ ├── types/ # TypeScript types
│ ├── utils/ # Utility functions
│ ├── routes.ts # Express routes
│ └── server.ts # Main server setup
└── certs/ # SSL certificates
```

## Feature Organization (`src/features/`)

```
feature-name/
├── __tests__/ # Vitest Unit tests
├── feature-name.routes.ts # API endpoints
├── feature-name.controller.ts # Business logic
├── feature-name.repository.ts # DB access (Prisma)
├── feature-name.helpers.ts # Helper functions (optional)
├── feature-name.validation.ts # Input validation (Zod schemas)
├── feature-name.types.ts # Types (optional)
├── feature-name.constants.ts # Constants (optional)
└── feature-name.admin.ts # Admin functions (optional)
```

### Feature Guidelines:

1.  **Single Responsibility Principle:** Each file has one job.
2.  **Naming:** `feature-name.file-type.ts` (e.g., `user.controller.ts`).
3.  **Shared Logic:** Use `src/core/` for cross-feature functionality. _Do not_ import directly between features.
4.  **Testing:** Unit tests in `__tests__` alongside the feature.

## Database (Prisma ORM)

- **Schema:** `prisma/schema.prisma` (Main model definition)
- **Extended Prisma Client**: Located at `<root>/src/lib/db` (Always import from here)
- **Migrations:** Not needed for this development state. Schema changes pushed directly.
- **Model Naming:** `snake_case` (tables), `camelCase` (fields).
- **Custom Methods:** Prisma Extensions in `src/lib/prisma/`.
- **Mocking:** Use mock from `src/lib/__mocks__/prisma.ts`.

## API Design (Hybrid Express/oRPC)

### oRPC Routes (Preferred - Type-Safe RPC)

- **Location:** `src/lib/orpc.ts` (core setup), `src/routes.ts` (main router)
- **Routes:** Feature-specific oRPC routers (e.g., `user.routes.ts`)
- **Validation:** Zod schemas with `.input(schema)` attachment
- **Authentication:** Middleware-based (`publicAuth`, `isLoggedInAuth`, `canMakeStateChangesAuth`, `adminAuth`)
- **Error Handling:** Automatic via `handleResponse()` utility and `ORPCError`
- **Type Safety:** End-to-end type safety from backend to frontend

### Express Routes (Legacy - Being Migrated)

- **Routes:** Feature-specific routing (e.g., `user.routes.ts`)
- **Validation:** Feature-specific zod schemas (e.g., `user.validation.ts`)
- **Response Format:** (via `routeHandler` utility):
    ```json
    {
      "success": true/false,
      "data": {},
      "error": ""
    }
    ```
- **RouteHandler Return Values:** Service functions should return:

    ```typescript
    // Success case
    return { data: yourData };

    // Error case
    return {
        error: "Error message",
        statusCode: 400, // Optional
    };
    ```

## Code Style (TypeScript)

- **TypeScript:** Preferred for new code.
- **Imports:** ES Modules (`import`/`export`).
- **Async/Await:** Use instead of callbacks/promises.
- **Logging:** Use winston logger utility for logging and use LogErrorStack to log errors with stack traces. (import {logger, LogErrorStack} from <root>/src/utils/log.js)
- **Constants:** `UPPER_SNAKE_CASE`.
- **Types:** Feature-specific `.types.ts` files.
- **Functional Programming:** Prefer functions over classes.
- **Feature Isolation**: Only use logic from core or within the feature.

## Testing (Vitest)

- **Framework:** Vitest.
- **Mocking**: Import prisma mocks from `src/_tests_/`.
- **Unit Tests:** Focus on business/gameplay logic and edge cases.
- **Location:** `__tests__` directory within feature.
- **File Naming:** `.test.ts` suffix.
- **Coverage:** Aim for high coverage.

## Documentation

- **JSDoc/TSDoc:** Use for function descriptions.
- **Comments:** Explain complex logic.

## Utilities and Libraries

### Core Utilities (`src/utils/`)

1.  **`routeHandler`:** Standardize API responses.
2.  **`log`:** Centralized logging.
3.  **`cacheHelper`:** In-memory caching.
4.  **`jsonHelper`** - JSON utilities.
5.  **`cdnUpload`** - Uploads to CDN storage.

### oRPC Core (`src/lib/orpc.ts`)

1.  **Context Creation:** Session and user context for all oRPC routes
2.  **Authentication Middleware:** Four levels of auth (`publicAuth`, `isLoggedInAuth`, `canMakeStateChangesAuth`, `adminAuth`)
3.  **Error Handling:** Automatic error logging and operational error detection
4.  **Type Safety:** Full TypeScript integration with frontend

### Shared Services (`src/core/`)

1.  **`inventory.service.ts`:** Manage user inventories.
2.  **`equipment.service.ts`:** Manage user equipment.
3.  **`achievement.service.ts`:** Manage achievements.

### Background Processing (`src/queues/scheduler/`)

1.  **`Scheduler`:** BullMQ-based job scheduling.
2.  **Task Categories:** Cleanups, health, missions, shops, auctions, casino, gangs, shrine.

### Usage Guide

#### General Guidelines:

- **Always** use `logger` instead of `console.log`
- Use **shared services** for cross-feature logic
- Use **scheduler** for background/scheduled tasks
- **Always** import the extended Prisma client from rather than using the Prisma client directly (import {db} from <root>/src/lib/db.js)

#### For oRPC Routes:

- Use appropriate auth middleware (`publicAuth`, `isLoggedInAuth`, `canMakeStateChangesAuth`, `adminAuth`)
- Use `.input(zodSchema)` for input validation
- Use `handleResponse()` to convert controller results to oRPC responses
- Throw `ORPCError` for expected errors, let unexpected errors bubble up
