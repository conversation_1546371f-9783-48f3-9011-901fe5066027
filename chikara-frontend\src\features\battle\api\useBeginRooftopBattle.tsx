import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useBeginRooftopBattle = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.battle.beginRooftopBattle.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: orpc.battle.getStatus.key() });
                queryClient.invalidateQueries({ queryKey: orpc.battle.rooftopList.key() });
                queryClient.invalidateQueries({ queryKey: APIROUTES.USER.CURRENTUSERINFO });
            },
        })
    );
};

export default useBeginRooftopBattle;
