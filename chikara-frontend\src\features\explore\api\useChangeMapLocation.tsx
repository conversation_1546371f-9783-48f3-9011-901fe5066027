import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";

const useChangeMapLocation = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.explore.changeMapLocation.mutationOptions({
            onSuccess: () => {
                // Invalidate and refetch the explore map to get updated location nodes
                queryClient.invalidateQueries({
                    queryKey: orpc.explore.getMapByLocation.key(),
                });
                queryClient.invalidateQueries({ queryKey: APIROUTES.USER.CURRENTUSERINFO });
            },
        })
    );
};

export default useChangeMapLocation;

// useQuery example
// const useExploreMap = (options: QueryOptions = {}) => {
//     return useQuery(
//         orpc.explore.getMapByLocation.queryOptions({
//             ...options,
//         })
//     );
// };
