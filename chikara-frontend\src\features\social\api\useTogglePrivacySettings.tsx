import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

// interface TogglePrivacySettingsParams {
//     showLastOnline: boolean;
// }

const useTogglePrivacySettings = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.social.updatePrivacySettings.mutationOptions({
            onSuccess: () => {
                toast.success("Privacy settings updated!");
                // Invalidate current user info to refresh with new settings
                queryClient.invalidateQueries({ queryKey: APIROUTES.USER.CURRENTUSERINFO });
            },
            onError: (error) => {
                toast.error(error.message || "Failed to update privacy settings");
            },
        })
    );
};

export default useTogglePrivacySettings;
