import { DisplayAvatar } from "@/components/DisplayAvatar";
import { APIROUTES } from "@/helpers/apiRoutes";
import { toast } from "react-hot-toast";
import { getUserInfo } from "@/hooks/api/useGetUserInfo";

const replyMessage = (id, navigate) => {
    toast.dismiss(id);
    navigate("/inbox");
};

export const ToastHandler = async (type, detailsObj, navigate, queryClient, setLevelupValue) => {
    let data;
    const details = typeof detailsObj === "string" ? JSON.parse(detailsObj) : detailsObj;

    switch (type) {
        case "profile_comment":
            if (details?.type === "suggestion_reply") {
                break;
            }
            if (details?.type === "suggestion") {
                toast.success("You received a comment on your suggestion!");
            }
            toast.success("You received a comment on your profile!");
            break;
        case "jail":
            toast.error("You were caught attacking another student and sent to jail!");
            break;
        case "hospitalised":
            if (details.reason === "character_encounter") {
                return;
            } else {
                await queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
                toast.error("You were knocked unconscious by another student!");
            }
            break;
        case "death_noted":
            toast.error(`A death book was used on you! You were injured!`);
            await queryClient.invalidateQueries({
                queryKey: APIROUTES.USER.CURRENTUSERINFO,
            });
            break;
        case "life_noted":
            toast.success(`A life book was used on you! You were fully healed!`);
            await queryClient.invalidateQueries({
                queryKey: APIROUTES.USER.CURRENTUSERINFO,
            });
            break;
        case "mission_completed":
            toast.success(`Your mission was completed!`);
            await queryClient.invalidateQueries({
                queryKey: APIROUTES.USER.CURRENTUSERINFO,
            });
            break;
        case "admin_action":
            if (details?.type === "item_gift") {
                toast.success(`A staff member sent you a gift!`);
                await queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.INVENTORY,
                });
            }
            if (details?.type === "chat_ban") {
                toast.success(
                    `You were banned from chat for ${details?.duration} minutes by Haruka Ito for ${details?.reason}`
                );
                await queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.INVENTORY,
                });
            }
            if (details?.type === "jail") {
                toast.success(`You were jailed for ${details?.duration} minutes by Haruka Ito for ${details?.reason}`);
                await queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.INVENTORY,
                });
            }
            break;
        case "levelup":
            setTimeout(() => {
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
                setLevelupValue(details.newLevel);
            }, 1500);
            break;
        case "message":
            queryClient.invalidateQueries({
                queryKey: APIROUTES.MESSAGING.HISTORY,
            });
            try {
                data = await getUserInfo(details.senderId);
            } catch (error) {
                console.error(error);
            }
            toast.custom((t) => (
                <div
                    className={`${
                        t.visible ? "animate-enter" : "animate-leave"
                    } pointer-events-auto flex w-full max-w-md rounded-lg bg-slate-800 shadow-lg ring-1 ring-gray-600/25`}
                >
                    <div className="w-0 flex-1 p-4">
                        <div className="flex items-start">
                            <div className="shrink-0 pt-0.5">
                                <DisplayAvatar className="size-10 rounded-full" src={data} />
                            </div>
                            <div className="ml-3 flex-1">
                                <p className="font-medium text-indigo-500 text-sm">{data.username}</p>
                                <p className="mt-1 text-gray-300 text-sm">Just sent you a message!</p>
                            </div>
                        </div>
                    </div>
                    <div className="flex border-gray-600 border-l">
                        <button
                            className="flex w-full items-center justify-center rounded-none rounded-r-lg border border-transparent p-4 font-medium text-blue-500 text-sm text-stroke-sm hover:text-blue-400 focus:outline-hidden focus:ring-2 focus:ring-blue-500"
                            onClick={() => replyMessage(t.id, navigate)}
                        >
                            View
                        </button>
                    </div>
                </div>
            ));

            break;
        default:
            console.log({ type, details });
    }
};
