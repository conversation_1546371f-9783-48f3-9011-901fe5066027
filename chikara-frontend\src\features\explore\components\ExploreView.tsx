import { useQueryClient } from "@tanstack/react-query";
import { <PERSON>, Grid3X3, <PERSON>ader2, <PERSON>, Sparkles } from "lucide-react";
import { useCallback, useMemo, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { NodeMapContainer } from "@/components/NodeMap";
import useExploreInteract from "@/features/explore/api/useExploreInteract";
import { APIROUTES } from "@/helpers/apiRoutes";
import { formatTimeUntilExpiration, isExpiringSoon } from "@/helpers/dateHelpers";
import { cn } from "@/lib/utils";
import { useNormalStore } from "../../../app/store/stores";
import { mapContainerVariants, nodeButtonVariants, cardBackgroundVariants } from "../styles/explore.styles";
import {
    getNodeTypeBadge,
    getNodeTypeColor,
    getNodeTypeIcon,
    getStatusBadge,
    getNodeTypeBackgroundHint,
    getNodeTypeVisualInfo,
} from "../styles/node.styles";
import type { ExploreNodeLocation, MapNodeData, ExploreCharacterDialogue } from "../types/explore.types";
import { sortNodesByStatus } from "../utils/explore.utils";
import { CharacterEncounterView } from "./CharacterEncounterView";
import { CommonLocations } from "./CommonLocations";
import { ExploreErrorState, ExploreLoadingState } from "./ExploreFetchState";
import { ForagingView } from "./ExploreForagingView";
import { MiningView } from "./ExploreMiningView";
import { ScavengeView } from "./ExploreScavengeView";
import { MapBackground } from "./MapBackground";
import type { StoryEpisodeData } from "@/features/story/types/story";
import { StoryEpisodePlayer } from "@/features/story/components/StoryEpisodePlayer";
import { orpc } from "@/lib/orpc";

interface ExploreViewProps {
    className?: string;
    viewType: "list" | "map";
    currentView?: ExploreNodeLocation | null;
    mapData: MapNodeData[] | undefined;
    isLoading: boolean;
    error: Error | null;
}

export const ExploreView = ({ mapData, isLoading, error, className, viewType, currentView }: ExploreViewProps) => {
    const mapContainerRef = useRef<HTMLDivElement>(null);

    const [selectedNodeId, setSelectedNodeId] = useState<number | null>(null);
    const [showGridOverlay, setShowGridOverlay] = useState(false);

    const { mutate: interactWithNode, isPending: isInteracting } = useExploreInteract({
        onSelectedNodeChange: setSelectedNodeId,
    });

    const { setJustJailed } = useNormalStore();
    const navigate = useNavigate();
    const queryClient = useQueryClient();

    const handleNodeClick = useCallback((nodeId: number) => {
        setSelectedNodeId(nodeId);
    }, []);

    // Find current node from map data
    const currentNode = mapData?.find((node) => node.status === "current");

    // Sort nodes by status (available first, then completed, then locked)
    const sortedNodes = useMemo(() => {
        if (!mapData) return [];
        return sortNodesByStatus(mapData);
    }, [mapData]);

    const handleNodeInteract = useCallback(
        (nodeId: number, shopId?: number | null) => {
            // Prevent interaction during travel

            const node = mapData?.find((n) => n.id === nodeId);
            if (!node || node.status !== "available") return;

            setSelectedNodeId(nodeId);
            const isStatic = node.isStatic;

            if (node.nodeType === "SHOP") {
                navigate(`/shops/${shopId || node.shopId || nodeId}`);
                return;
            }

            interactWithNode({ nodeId, isStatic });
        },
        [mapData, interactWithNode, navigate]
    );

    const handleCloseEncounter = async () => {
        queryClient.invalidateQueries({
            queryKey: APIROUTES.USER.CURRENTUSERINFO,
        });
        setJustJailed(false);
        await queryClient.invalidateQueries({
            queryKey: orpc.explore.getMapByLocation.key(),
        });
    };

    const handleCloseStoryEpisode = async () => {
        await queryClient.invalidateQueries({
            queryKey: orpc.explore.getMapByLocation.key(),
        });
    };

    // Check if there's a current character encounter node with encounter data
    if (currentNode && currentNode.nodeType === "CHARACTER_ENCOUNTER" && currentNode.metadata?.dialogue) {
        return (
            <CharacterEncounterView
                dialogue={currentNode.metadata.dialogue as ExploreCharacterDialogue}
                healed={currentNode.metadata.healed as boolean | undefined}
                nodeId={currentNode.id}
                onClose={handleCloseEncounter}
            />
        );
    }

    if (currentNode && currentNode.nodeType === "STORY" && currentNode.metadata?.episodeData) {
        return (
            <StoryEpisodePlayer
                episodeData={currentNode.metadata.episodeData as StoryEpisodeData}
                onClose={handleCloseStoryEpisode}
            />
        );
    }

    // Check if there's a current scavenging node
    if (currentNode && currentNode.nodeType === "SCAVENGE_NODE" && currentNode.metadata?.choices) {
        return (
            <ScavengeView
                nodeId={currentNode.id}
                location={currentNode.location}
                choices={currentNode.metadata.choices as string[]}
                onClose={handleCloseEncounter}
            />
        );
    }

    // Check if there's a current mining node
    if (currentNode && currentNode.nodeType === "MINING_NODE" && currentNode.status === "current") {
        const miningType = (currentNode.metadata?.miningType as string) || "ore";
        const difficulty = (currentNode.metadata?.difficulty as "easy" | "medium" | "hard") || "easy";

        return (
            <MiningView
                nodeId={currentNode.id}
                location={currentNode.location}
                miningType={miningType}
                difficulty={difficulty}
                onClose={handleCloseEncounter}
            />
        );
    }

    // Check if there's a current foraging node
    if (currentNode && currentNode.nodeType === "FORAGING_NODE" && currentNode.status === "current") {
        const foragingType = (currentNode.metadata?.foragingType as string) || "herbs";
        const difficulty = (currentNode.metadata?.difficulty as "easy" | "medium" | "hard") || "easy";

        return (
            <ForagingView
                nodeId={currentNode.id}
                location={currentNode.location}
                foragingType={foragingType}
                difficulty={difficulty}
                onClose={handleCloseEncounter}
            />
        );
    }

    if (isLoading) {
        return <ExploreLoadingState />;
    }

    if (error) {
        return <ExploreErrorState />;
    }

    if (viewType === "map") {
        return (
            <div className={`relative ${className || ""}`}>
                {isInteracting && (
                    <div className="absolute inset-0 bg-black/20 z-50 flex items-center justify-center rounded-lg">
                        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg flex items-center space-x-2">
                            <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
                            <span className="text-gray-700 dark:text-gray-300">Interacting...</span>
                        </div>
                    </div>
                )}

                {/* Desktop Layout: Sidebar + Map */}
                <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
                    {/* Map Container */}
                    <div ref={mapContainerRef} className="flex-1 min-w-0 relative min-h-[400px] lg:min-h-[600px]">
                        {/* Map background */}
                        <div className="absolute inset-0 overflow-hidden rounded-lg">
                            <MapBackground currentView={currentView || "shibuya"} className="w-full h-full" />
                        </div>

                        {/* Development Grid Overlay Toggle */}
                        {import.meta.env.MODE === "development" && (
                            <div className="absolute top-4 left-4 z-20">
                                <button
                                    title="Toggle 5x5 Grid Overlay"
                                    className={cn(
                                        "flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                                        "bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm",
                                        "border border-gray-200 dark:border-gray-700",
                                        "hover:bg-white dark:hover:bg-gray-800",
                                        showGridOverlay
                                            ? "text-blue-600 dark:text-blue-400 border-blue-300 dark:border-blue-600"
                                            : "text-gray-600 dark:text-gray-400"
                                    )}
                                    onClick={() => setShowGridOverlay(!showGridOverlay)}
                                >
                                    <Grid3X3 className="w-4 h-4" />
                                    <span className="hidden sm:inline">Grid</span>
                                </button>
                            </div>
                        )}

                        {/* Node overlay */}
                        <div className="relative z-10 w-full h-full">
                            <NodeMapContainer
                                showTooltips
                                showAnimations
                                showGridOverlay={showGridOverlay}
                                nodes={mapData || []}
                                connections={[]}
                                mapType="isolated" // Using isolated type for explore nodes
                                interactive={false} // Disable map controls (pan, zoom, etc.)
                                className={cn(
                                    mapContainerVariants({ background: "default" }),
                                    "bg-transparent border-0 w-full h-full" // Make container transparent to show map
                                )}
                                onNodeClick={handleNodeClick}
                                onAccessNode={handleNodeInteract}
                            />
                        </div>
                    </div>
                    <div className="lg:w-80 lg:flex-shrink-0">
                        <CommonLocations />
                    </div>
                </div>
            </div>
        );
    }

    // Enhanced List view with better cards
    return (
        <div className={cn("space-y-6 grid grid-cols-1 lg:grid-cols-2 gap-12", className)}>
            {/* Enhanced List View */}
            <div className="space-y-3">
                <div className="mb-4">
                    <div className="flex items-center gap-2 mb-2">
                        <div className="w-1 h-6 bg-gradient-to-b from-purple-500 to-pink-500 rounded-full" />
                        <h2 className="text-xl font-bold text-gray-900 dark:text-white">District Locations</h2>
                        <Sparkles className="w-4 h-4 text-purple-500 animate-pulse" />
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Explore what this district has to offer</p>
                </div>

                <div className="space-y-3">
                    {sortedNodes.map((node, index) => {
                        const statusBadge = getStatusBadge(node.status);
                        const typeBadge = getNodeTypeBadge(node.nodeType);
                        const visualInfo = getNodeTypeVisualInfo(node.nodeType);
                        const isAvailable = node.status === "available";
                        const isLocked = node.status === "locked";
                        const isInteractable = isAvailable && !isInteracting && selectedNodeId !== node.id;

                        return (
                            <div
                                key={node.id}
                                className="relative p-1 -m-1"
                                style={{ animationDelay: `${index * 50}ms` }}
                            >
                                <button
                                    disabled={!isAvailable || isInteracting || selectedNodeId === node.id}
                                    className={cn(
                                        nodeButtonVariants({
                                            status: node.status,
                                            interactive: isInteractable,
                                            nodeType: node.nodeType,
                                        }),
                                        cardBackgroundVariants({
                                            nodeType: node.nodeType,
                                        }),
                                        isInteractable && visualInfo.hoverBorder,
                                        "w-full relative min-h-[80px] overflow-hidden",
                                        isInteractable && "transform transition-all duration-300 hover:scale-[1.02]"
                                    )}
                                    onClick={() => isAvailable && handleNodeInteract(node.id, node.shopId || null)}
                                >
                                    {/* Background Pattern */}
                                    <div
                                        className={cn(
                                            "absolute inset-0 opacity-5 dark:opacity-10 bg-cover bg-center bg-no-repeat transition-opacity duration-300",
                                            isInteractable && "group-hover:opacity-10 dark:group-hover:opacity-20"
                                        )}
                                        style={{
                                            backgroundImage: (() => {
                                                const hint = getNodeTypeBackgroundHint(node.nodeType);
                                                if (!hint) return "none";
                                                const match = hint.match(/bg-\[url\('([^']+)'\)\]/);
                                                return match ? `url("${match[1]}")` : "none";
                                            })(),
                                        }}
                                    />

                                    {/* Glow effect for available items */}
                                    {isAvailable && (
                                        <div
                                            className={cn(
                                                "absolute inset-0 opacity-0 transition-opacity duration-300 pointer-events-none",
                                                isInteractable && "group-hover:opacity-100",
                                                visualInfo.shadowColor,
                                                "shadow-2xl rounded-xl"
                                            )}
                                        />
                                    )}

                                    {/* Special story node glow effect */}
                                    {isAvailable && node.nodeType === "STORY" && (
                                        <div className="absolute inset-0 bg-gradient-to-r from-amber-400/20 to-yellow-400/20 rounded-xl animate-pulse pointer-events-none" />
                                    )}

                                    <div className="relative z-10 flex items-center gap-3 p-0">
                                        {/* Enhanced Icon */}
                                        <div className="relative flex-shrink-0">
                                            <div
                                                className={cn(
                                                    "w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 text-white shadow-md bg-gradient-to-br relative transition-all duration-300",
                                                    isLocked && "grayscale opacity-60",
                                                    isInteractable && "group-hover:shadow-xl",
                                                    getNodeTypeColor(node.nodeType)
                                                )}
                                            >
                                                <div className="w-4 h-4">{getNodeTypeIcon(node.nodeType)}</div>
                                                {isLocked && (
                                                    <div className="absolute -top-0.5 -right-0.5 w-4 h-4 rounded-full bg-gray-900 dark:bg-gray-100 border border-gray-700 dark:border-gray-300 flex items-center justify-center">
                                                        <Lock className="w-2 h-2 text-gray-400 dark:text-gray-600" />
                                                    </div>
                                                )}
                                                {isAvailable && node.nodeType === "STORY" && (
                                                    <div className="absolute -top-0.5 -right-0.5 w-3 h-3 rounded-full bg-amber-500 animate-pulse shadow-md shadow-amber-500/50" />
                                                )}
                                                {isAvailable && node.nodeType !== "STORY" && (
                                                    <div className="absolute -top-0.5 -right-0.5 w-3 h-3 rounded-full bg-green-500 animate-pulse shadow-md shadow-green-500/50" />
                                                )}
                                            </div>
                                        </div>

                                        {/* Content */}
                                        <div className="flex-1 min-w-0 text-left">
                                            {/* Header with badges */}
                                            <div className="flex items-center justify-between mb-1">
                                                <div className="flex items-center gap-2 min-w-0 flex-1">
                                                    <h3 className="text-base font-bold text-gray-900 dark:text-white line-clamp-1">
                                                        {node.title}
                                                    </h3>
                                                    {node.nodeType === "STORY" && (
                                                        <Sparkles className="w-4 h-4 text-amber-500 animate-pulse flex-shrink-0" />
                                                    )}
                                                </div>
                                                <div className="flex items-center gap-1 flex-shrink-0 ml-2">
                                                    <span
                                                        className={cn(
                                                            "px-2 py-0.5 rounded text-xs font-bold",
                                                            typeBadge.color,
                                                            node.nodeType === "STORY" &&
                                                                "ring-2 ring-amber-300 dark:ring-amber-600"
                                                        )}
                                                    >
                                                        {typeBadge.text}
                                                    </span>
                                                    {!node.isStatic &&
                                                        node.status !== "available" &&
                                                        node.status !== "current" && (
                                                            <span
                                                                className={cn(
                                                                    "px-1.5 py-0.5 rounded text-xs font-medium",
                                                                    statusBadge.color
                                                                )}
                                                            >
                                                                {statusBadge.text}
                                                            </span>
                                                        )}
                                                </div>
                                            </div>

                                            {/* Description and hint combined */}
                                            <div className="space-y-1">
                                                <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-1 leading-tight">
                                                    {node.description}
                                                </p>
                                                <p className="text-xs text-gray-500 dark:text-gray-400 italic">
                                                    {node.nodeType === "STORY"
                                                        ? "Story episode - advances main quest progress"
                                                        : visualInfo.description}
                                                </p>
                                            </div>

                                            {/* Expiration info for non-static nodes */}
                                            {node.expiresAt && !node.isStatic && (
                                                <div className="mt-2">
                                                    <div
                                                        className={cn(
                                                            "flex items-center gap-1 text-xs px-2 py-1 rounded w-fit",
                                                            isExpiringSoon(node.expiresAt)
                                                                ? "bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300"
                                                                : "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300"
                                                        )}
                                                    >
                                                        <Clock className="w-3 h-3" />
                                                        <span className="font-medium">
                                                            {formatTimeUntilExpiration(node.expiresAt)}
                                                        </span>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </button>
                            </div>
                        );
                    })}
                </div>
            </div>

            {/* Common Locations Section for List View */}
            <CommonLocations />
        </div>
    );
};
