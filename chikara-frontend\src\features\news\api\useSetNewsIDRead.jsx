import { APIROUTES } from "@/helpers/apiRoutes";
import axios from "@/helpers/axiosInstance";
import { useMutation, useQueryClient } from "@tanstack/react-query";

const useSetNewsIDRead = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async (newsID) => {
            return await axios.post(`${APIROUTES.USER.SETLASTNEWSIDREAD}`, { id: newsID });
        },
        onSuccess: () => {
            queryClient.invalidateQueries({
                queryKey: APIROUTES.USER.CURRENTUSERINFO,
            });
        },
        onError: (error) => {
            console.log(error?.response?.data || "An error occurred");
            //   toast.error(`Error: ${error.message}`);
        },
    });

    return {
        setNewsID: mutation,
    };
};

export default useSetNewsIDRead;
