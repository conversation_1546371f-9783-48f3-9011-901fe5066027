import { APIROUTES } from "@/helpers/apiRoutes";
import { useQueryClient } from "@tanstack/react-query";
import { isValid } from "date-fns";
import Countdown from "react-countdown";

export const CountdownTimer = ({ targetDate, showHours, showSeconds = true, showHoursText }) => {
    const queryClient = useQueryClient();

    const isValidDate = isValid(targetDate);
    if (!isValidDate) return null;

    const renderer = ({ formatted: { hours, minutes, seconds }, completed }) => {
        if (completed) {
            return null;
        } else {
            return (
                <span className="text-white font-medium font-display">
                    {showHours && hours + ":"}
                    {minutes}
                    {showSeconds && ":" + seconds}
                    {showHoursText && "hrs"}
                </span>
            );
        }
    };

    return (
        <Countdown
            key={targetDate}
            data-testid="countdown-timer"
            date={targetDate + 1000}
            renderer={renderer}
            onComplete={() =>
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                })
            }
        />
    );
};
