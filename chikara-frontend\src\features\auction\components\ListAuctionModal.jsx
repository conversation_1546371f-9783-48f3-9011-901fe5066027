import yenImg from "@/assets/icons/UI/yen.png";
import { DisplayItem } from "@/components/DisplayItem";
import DisplayShopItem from "@/components/Items/DisplayShopItem";
import { Modal } from "@/components/Modal/Modal";
import { APIROUTES } from "@/helpers/apiRoutes";
import useGameConfig from "@/hooks/useGameConfig";
import { orpc } from "@/lib/orpc";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { toast } from "react-hot-toast";

export default function ListAuctionModal({ openModal, setOpenModal, itemToSell, setItemToSell }) {
    const { ALLOWED_AUCTION_ITEM_TYPES, BLACKLISTED_AUCTION_ITEM_IDS } = useGameConfig("auctionConfig");

    function filterItems(userItems) {
        const items = userItems?.filter((item) => ALLOWED_AUCTION_ITEM_TYPES?.includes(item.item.itemType)) || [];
        const itemsWithoutBlacklistedIds =
            items?.filter((item) => !BLACKLISTED_AUCTION_ITEM_IDS?.includes(item.item.id)) || [];
        return itemsWithoutBlacklistedIds;
    }

    const { data: itemList } = useQuery(orpc.user.getTradeableInventory.queryOptions({ select: filterItems }));

    const [quantity, setQuantity] = useState(1);
    const [length, setLength] = useState(12);
    const [buyoutPrice, setBuyoutPrice] = useState(100);
    const [bankDeposit, setBankDeposit] = useState(false);

    const queryClient = useQueryClient();

    const item = { ...itemToSell?.item };
    if (item.rarity === "novice") {
        item.colour = "text-stroke-s-sm";
    }
    if (item.rarity === "standard") {
        item.colour = "text-green-600";
    }
    if (item.rarity === "enhanced") {
        item.colour = "text-blue-600";
    }
    if (item.rarity === "specialist") {
        item.colour = "text-indigo-600";
    }
    if (item.rarity === "military") {
        item.colour = "text-red-600";
    }
    if (item.rarity === "legendary") {
        item.colour = "text-yellow-600";
    }

    const listItem = useMutation(
        orpc.auction.createListing.mutationOptions({
            onSuccess: () => {
                handleClose();
                toast.success(
                    `Successfully listed ${parseInt(quantity)}x ${item.name} for ¥${parseInt(buyoutPrice) * parseInt(quantity)}`
                );
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.INVENTORY,
                });
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.AUCTIONS.AUCTIONLIST,
                });
            },
            onError: (error) => {
                console.log(error);
                toast.error(error.message);
            },
        })
    );

    const handleListItem = () => {
        if (!itemToSell) {
            toast.error("You must select an item to list");
            return;
        }
        if (itemToSell?.upgradeLevel > 0) {
            toast.error("You cannot list an upgraded item");
            return;
        }
        if (!itemToSell?.isTradeable) {
            toast.error("You cannot list this item on the market");
            return;
        }
        listItem.mutate({
            itemId: parseInt(itemToSell.item.id),
            quantity: parseInt(quantity),
            buyoutPrice: parseInt(buyoutPrice),
            auctionLength: parseInt(length),
            bankFunds: bankDeposit,
        });
    };

    const handleClose = () => {
        setOpenModal(false);
        setQuantity(1);
        setLength(12);
        setBuyoutPrice(100);
        setBankDeposit(false);
        setTimeout(() => setItemToSell(null), 500);
    };

    const listingFees = {
        12: 0.03,
        24: 0.06,
        48: 0.09,
    };

    const bankFee = 0.15;

    const handleQuantityChange = (value) => {
        if (!itemToSell) {
            toast.error("You must select an item to list");
            return;
        }
        if (value > itemToSell.count) {
            setQuantity(itemToSell.count);
        } else {
            setQuantity(value);
        }
    };

    return (
        <Modal
            showClose
            open={openModal}
            title="Inventory"
            iconBackground="shadow-lg"
            contentHeight="max-h-[75dvh] md:max-h-[60dvh]"
            className="min-w-[50dvw]! lg:min-w-[35dvw]! mx-auto"
            Icon={() => (
                <img
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/4AAKgyV.png`}
                    alt=""
                    className="mt-0.5 h-11 w-auto"
                />
            )}
            onOpenChange={handleClose}
        >
            <div className="overflow-auto! mt-3 h-full text-center">
                {itemToSell ? (
                    <div className="mb-1 flex flex-col">
                        <div className="mb-6 flex flex-col">
                            <DisplayItem item={itemToSell.item} height="w-1/4 max-w-36 mx-auto" />
                            <p className="text-gray-200">{itemToSell.item.name}</p>
                            <p className="mx-auto flex text-gray-200">
                                Sell Value:{" "}
                                <span className="ml-2 inline-flex gap-1 text-yellow-500">
                                    <img src={yenImg} alt="" className="my-auto size-4" />
                                    {itemToSell.item.cashValue}
                                </span>
                            </p>
                        </div>
                        <div className="mx-auto flex w-full flex-col rounded-lg border border-slate-700 bg-slate-800 px-8 py-4 md:w-3/4 2xl:px-20">
                            <div className="mx-auto my-2 flex w-full rounded-md shadow-xs md:mt-1">
                                <label
                                    className="my-auto mr-2 block w-1/4 text-center font-medium text-gray-700 text-xs uppercase tracking-wide dark:text-gray-300"
                                    htmlFor="quantity"
                                >
                                    Price
                                    <p>(per item)</p>
                                </label>

                                <div className="mt-1 flex flex-1 rounded-md shadow-xs">
                                    <span className="inline-flex items-center rounded-l-md border border-gray-500 border-r-0 bg-gray-900 px-3 text-gray-300 text-shadow sm:text-sm">
                                        ¥
                                    </span>
                                    <input
                                        type="number"
                                        name="amount"
                                        id="amount"
                                        className="block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-500 px-3 py-2 text-lg placeholder:text-gray-400 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:bg-gray-900 dark:text-gray-300"
                                        placeholder="100"
                                        min="100"
                                        max={1000000}
                                        value={buyoutPrice}
                                        onChange={(e) => setBuyoutPrice(e.target.value)}
                                    />
                                </div>
                            </div>
                            <div className="mx-auto my-2 flex w-full rounded-md shadow-xs md:mt-1">
                                <label
                                    className="my-auto mr-2 block w-1/4 text-center font-medium text-gray-700 text-xs uppercase tracking-wide dark:text-gray-300"
                                    htmlFor="quantity"
                                >
                                    Quantity
                                </label>
                                <input
                                    type="number"
                                    id="quantity"
                                    name="quantity"
                                    className="block w-full min-w-0 flex-1 rounded-l-md border-gray-300 px-3 text-center text-lg focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm md:text-xl dark:border-gray-500 dark:bg-gray-900 dark:text-white"
                                    placeholder={1}
                                    min={1}
                                    step={1}
                                    max={itemToSell.count}
                                    value={quantity}
                                    onChange={(e) => {
                                        handleQuantityChange(e.target.value);
                                    }}
                                />
                                <span className="inline-flex items-center rounded-r-md border border-gray-300 border-l-0 bg-gray-50 px-3 text-center text-gray-500 text-lg ring-gray-500 sm:text-sm md:text-xl dark:border-gray-500 dark:bg-gray-900 dark:text-white">
                                    / {itemToSell.count}
                                </span>
                            </div>
                            <div className="mx-auto my-2 flex w-full rounded-md shadow-xs md:mt-1">
                                <label
                                    className="my-auto mr-2 block w-1/4 text-center font-medium text-gray-700 text-xs uppercase tracking-wide dark:text-gray-300"
                                    htmlFor="quantity"
                                >
                                    Length
                                </label>
                                <select
                                    id="filter-item-name-box"
                                    className="mx-auto w-full flex-1 rounded-md border border-gray-300 text-left font-medium text-gray-700 text-lg shadow-xs focus:border-indigo-500 focus:outline-hidden focus:ring-1 focus:ring-indigo-500 sm:text-sm md:text-base dark:border-gray-500 dark:bg-gray-900 dark:text-white"
                                    onChange={(e) => setLength(e.target.value)}
                                >
                                    <option value="12">12 Hours</option>
                                    <option value="24">24 Hours</option>
                                    <option value="48">48 Hours</option>
                                </select>
                            </div>

                            <div className="mx-auto my-1 flex items-center">
                                <input
                                    id="bank_deposit"
                                    name="bank_deposit"
                                    type="checkbox"
                                    defaultChecked={bankDeposit}
                                    className="size-4 rounded-sm border-gray-300 bg-gray-100 text-blue-600 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-blue-600"
                                    onChange={(e) => setBankDeposit(e.target.checked)}
                                />
                                <label
                                    htmlFor="remember_me"
                                    className="ml-2 block text-gray-200 text-sm text-stroke-sm"
                                >
                                    Deposit funds to bank ({bankFee * 100}% fee)
                                </label>
                            </div>
                            <div className="mx-auto mt-2 flex w-full flex-col rounded-lg border border-gray-600 bg-gray-700 px-2 py-0.5 text-stroke-sm text-white">
                                <div className="grid grid-cols-2"></div>
                                <p className="text-gray-200">
                                    Fee: <span className="text-red-500">{listingFees[length] * 100}%</span>
                                </p>
                                <p className="text-gray-200">
                                    Listing Cost:{" "}
                                    <span className="text-red-500">
                                        ¥{Math.max(buyoutPrice * listingFees[length], 50).toFixed(0)}
                                    </span>
                                </p>
                                <p className="mx-auto mt-2 w-fit rounded-lg px-2 text-base text-stroke-sm text-white md:text-lg">
                                    Listing{" "}
                                    <span className={item?.colour}>
                                        {Math.max(quantity, 1)}x {item?.name}
                                    </span>{" "}
                                    for{" "}
                                    <span className=" text-green-600">
                                        ¥{Math.max(buyoutPrice * quantity, buyoutPrice)}
                                    </span>{" "}
                                    Total
                                </p>
                            </div>
                        </div>

                        <button
                            type="button"
                            className="darkBlueButtonBGSVG mx-auto mt-2 flex h-18 w-3/4 items-center justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs md:mt-3 md:w-1/2 dark:text-slate-200"
                            onClick={handleListItem}
                        >
                            List Item
                        </button>
                    </div>
                ) : (
                    <div className="mt-2 grid grid-cols-3 gap-x-3 gap-y-4 sm:gap-x-6 md:mt-6 lg:grid-cols-6 xl:gap-x-3">
                        {itemList?.map((inventoryItem) => (
                            <SingleItem key={inventoryItem.id} item={inventoryItem} setItemToSell={setItemToSell} />
                        ))}
                    </div>
                )}

                {/* <table className=" divide-y divide-gray-200 dark:divide-gray-600 ">
          <thead className="bg-gray-50 dark:bg-gray-800 dark:text-gray-200">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider "
              >
                Item
              </th>

              <th
                scope="col"
                className="px-2 py-3 text-center text-xs font-medium uppercase tracking-wider  md:px-6"
              >
                Qty
              </th>
            </tr>
          </thead>

          <tbody className="divide-y divide-gray-200 bg-white dark:bg-slate-800 dark:divide-gray-600">
            {itemList?.map((item) => (
              <tr key={item.id}>
                <td className="whitespace-nowrap pl-3 pr-1 md:px-6 py-0.5">
                  <div className="flex items-center h-fit">
                    <DisplayItem item={item} height="h-8 md:h-7" />
                    <div className="ml-4">
                      <div
                        className={cn(
                          "md:text-md text-sm font-medium",
                          rarityColours(item.item.rarity),
                          item.item.name.length > 18 ? "text-xs" : "",
                        )}
                      >
                        {item.item.name}{" "}
                        {item.upgradeLevel > 0 && <span>+{item.upgradeLevel}</span>}
                      </div>
                      <div className="block text-xs md:hidden dark:text-gray-200">
                        {displayItemType(item.item.itemType)}
                      </div>
                    </div>
                  </div>
                </td>

                <td className="whitespace-nowrap px-0 text-center md:px-6">
                  <div className="text-sm text-gray-700 dark:text-gray-200">{item.count}</div>
                </td>
              </tr>
            ))}
          </tbody>
        </table> */}
            </div>
        </Modal>
    );
}

const SingleItem = ({ item, setItemToSell }) => {
    return (
        <div
            key={item.id}
            className="group cursor-pointer divide-gray-200 rounded-lg shadow-sm"
            onClick={() => {
                setItemToSell(item);
            }}
        >
            <DisplayShopItem item={item.item} type="sell" count={item.count} />

            <div className="-mt-1.5 flex flex-col">
                <div className="relative mx-auto flex w-[90%] items-center justify-center rounded-b-md border border-transparent bg-gray-100 pt-3 pb-2 font-medium text-base text-gray-900 ring-2 ring-black group-hover:bg-gray-800 md:text-base dark:border dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 dark:group-hover:border-gray-400">
                    List Item
                </div>
            </div>
        </div>
    );
};
