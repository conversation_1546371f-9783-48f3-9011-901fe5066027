import { admin<PERSON><PERSON>, moderatorAuth, isLoggedInAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as AdminService from "./admin.controller.js";
import adminSchema from "./admin.validation.js";
import fetchIcons from "../../utils/fetchIcons.js";
export const adminRouter = {
    getUserInfo: adminAuth.input(adminSchema.getUserInfo).handler(async ({ input }) => {
        const response = await AdminService.GetFullUserInfo(input.id);
        return handleResponse(response);
    }),
    getGameConfig: adminAuth.handler(async () => {
        const response = await AdminService.GetFullGameConfig();
        return handleResponse(response);
    }),
    getEquippedValues: adminAuth.input(adminSchema.getEquippedValues).handler(async ({ input }) => {
        const response = await AdminService.GetUserEquippedValues(input.id);
        return handleResponse(response);
    }),
    getActiveUsersStats: adminAuth.input(adminSchema.getActiveUsersStats).handler(async ({ input }) => {
        const startDate = new Date(input.startDate);
        const endDate = new Date(input.endDate);
        const response = await AdminService.ActiveUsersStats(startDate, endDate);
        return handleResponse(response);
    }),
    getRegistrationStats: adminAuth.input(adminSchema.getRegistrationStats).handler(async ({ input }) => {
        const startDate = new Date(input.startDate);
        const endDate = new Date(input.endDate);
        const response = await AdminService.RegistrationStats(startDate, endDate);
        return handleResponse(response);
    }),
    getTotalUsers: adminAuth.handler(async () => {
        const response = await AdminService.TotalUsers();
        return handleResponse(response);
    }),
    getUserList: adminAuth.handler(async () => {
        const response = await AdminService.GetFullUserList();
        return handleResponse(response);
    }),
    getCurrentActiveUsers: adminAuth.handler(async () => {
        const response = await AdminService.CurrentActiveUsers();
        return handleResponse(response);
    }),
    getCirculatingYenWeekly: adminAuth.handler(async () => {
        const response = await AdminService.GetCirculatingYenThisWeek();
        return handleResponse(response);
    }),
    getGangInfo: isLoggedInAuth.input(adminSchema.getGangInfo).handler(async ({ input }) => {
        const response = await AdminService.GetFullGangInfo(input.id);
        return handleResponse(response);
    }),
    getQuitPlayers: adminAuth.handler(async () => {
        const response = await AdminService.FindQuitPlayers();
        return handleResponse(response);
    }),
    getLatestLogs: adminAuth.handler(async ({ context }) => {
        const response = await AdminService.getLatestLogs(context.user.id);
        return handleResponse(response);
    }),
    getBattles: adminAuth.handler(async () => {
        const response = await AdminService.getBattlesList();
        return handleResponse(response);
    }),
    fetchIcons: adminAuth.input(adminSchema.fetchIcons).handler(async ({ input }) => {
        const icons = await fetchIcons.getAllIcons(input.path);
        return { data: icons };
    }),
    chatBanUser: adminAuth.input(adminSchema.chatBanUser).handler(async ({ input, context }) => {
        const response = await AdminService.ChatBanUser(input.userId, input.timeMS, context.user.id);
        return handleResponse(response);
    }),
    removeUserChatMessages: adminAuth.input(adminSchema.removeUserChatMessages).handler(async ({ input, context }) => {
        const response = await AdminService.RemoveUserChatMessages(input.userId, context.user.id);
        return handleResponse(response);
    }),
    hideSingleMessage: moderatorAuth.input(adminSchema.hideSingleMessage).handler(async ({ input, context }) => {
        const response = await AdminService.HideSingleChatMessage(input.messageId, context.user.id);
        return handleResponse(response);
    }),
    unhideSingleMessage: moderatorAuth.input(adminSchema.unhideSingleMessage).handler(async ({ input, context }) => {
        const response = await AdminService.UnhideSingleChatMessage(input.messageId, context.user.id);
        return handleResponse(response);
    }),
    deleteSingleMessage: adminAuth.input(adminSchema.deleteSingleMessage).handler(async ({ input, context }) => {
        const response = await AdminService.DeleteSingleChatMessage(input.messageId, context.user.id);
        return handleResponse(response);
    }),
    banUser: adminAuth.input(adminSchema.banUser).handler(async ({ input, context }) => {
        const response = await AdminService.BanUser(input.userId, input.timeMS, context.user.id);
        return handleResponse(response);
    }),
    jailUser: adminAuth.input(adminSchema.jailUser).handler(async ({ input, context }) => {
        const response = await AdminService.JailUser(input.userId, input.timeMS, input.jailReason, context.user.id);
        return handleResponse(response);
    }),
    bailUser: adminAuth.input(adminSchema.bailUser).handler(async ({ input, context }) => {
        const response = await AdminService.BailUser(input.userId, context.user.id);
        return handleResponse(response);
    }),
    reviveUser: adminAuth.input(adminSchema.reviveUser).handler(async ({ input, context }) => {
        const userId = input.userId || context.user.id;
        const response = await AdminService.ReviveUser(userId, context.user.id);
        return handleResponse(response);
    }),
    profileDetailsBan: adminAuth.input(adminSchema.profileDetailsBan).handler(async ({ input, context }) => {
        const response = await AdminService.ProfileDetailsChangeBan(input.id, input.timeMS, context.user.id);
        return handleResponse(response);
    }),
    giveItem: adminAuth.input(adminSchema.giveItem).handler(async ({ input, context }) => {
        const userId = input.id || context.user.id;
        const response = await AdminService.GiveItem(input.itemName, input.amount, userId, context.user.id, input.message);
        return handleResponse(response);
    }),
    removeItem: adminAuth.input(adminSchema.removeItem).handler(async ({ input, context }) => {
        const response = await AdminService.RemoveItem(input.itemName, input.amount, input.id, context.user.id, input.itemId);
        return handleResponse(response);
    }),
    bulkCreateItems: adminAuth.input(adminSchema.bulkCreateItems).handler(async ({ input }) => {
        const response = await AdminService.bulkCreateItems(input.items);
        return handleResponse(response);
    }),
    updateUserValues: adminAuth.input(adminSchema.updateUserValues).handler(async ({ input, context }) => {
        const response = await AdminService.UpdateUserValues(input.userId, input.type, input.value, context.user.id);
        return handleResponse(response);
    }),
    updateUserMoney: adminAuth.input(adminSchema.updateUserMoney).handler(async ({ input, context }) => {
        const response = await AdminService.UpdateUserMoney(input.userId, input.method, input.type, input.value, context.user.id);
        return handleResponse(response);
    }),
    updateUserStats: adminAuth.input(adminSchema.updateUserStats).handler(async ({ input, context }) => {
        const response = await AdminService.UpdateUserStats(input.userId, input.targetStat, input.value, input.method, context.user.id);
        return handleResponse(response);
    }),
    updateAdminNotes: adminAuth.input(adminSchema.updateAdminNotes).handler(async ({ input, context }) => {
        const response = await AdminService.UpdateAdminNotes(input.userId, input.notes, context.user.id);
        return handleResponse(response);
    }),
    resetUserRoguelikeData: adminAuth.input(adminSchema.resetUserRoguelikeData).handler(async ({ input, context }) => {
        const response = await AdminService.ResetUserRoguelikeData(input.id, context.user.id);
        return handleResponse(response);
    }),
    updateUserRoguelikeData: adminAuth
        .input(adminSchema.updateUserRoguelikeData)
        .handler(async ({ input, context }) => {
        const response = await AdminService.UpdateUserRoguelikeData(input.id, input.mapdata, input.level, context.user.id);
        return handleResponse(response);
    }),
    updateUserRoguelikeBuffs: adminAuth
        .input(adminSchema.updateUserRoguelikeBuffs)
        .handler(async ({ input, context }) => {
        const response = await AdminService.UpdateUserRoguelikeBuffs(input.userId, input.strBuff, input.defBuff, context.user.id);
        return handleResponse(response);
    }),
    updateUserAvatar: adminAuth.input(adminSchema.updateUserAvatar).handler(async ({ input, context }) => {
        const response = await AdminService.UpdateUserAvatar(input.id, input.avatar, context.user.id);
        return handleResponse(response);
    }),
    endLottery: adminAuth.handler(async () => {
        const response = await AdminService.EndLottery();
        return handleResponse(response);
    }),
    sendPatchNotesNotify: adminAuth.input(adminSchema.sendPatchNotesNotify).handler(async ({ input }) => {
        const response = await AdminService.SendPatchNotesGlobalNotification(input.id);
        return handleResponse(response);
    }),
    createTestUser: adminAuth.handler(async () => {
        const response = await AdminService.CreateTestUser();
        return handleResponse(response);
    }),
    createAuctionListing: adminAuth.input(adminSchema.createAuctionListing).handler(async ({ input, context }) => {
        const response = await AdminService.CreateAuctionListing(input.itemId, context.user.id);
        return handleResponse(response);
    }),
    sendTestPushNotification: adminAuth
        .input(adminSchema.sendTestPushNotification)
        .handler(async ({ input, context }) => {
        const userId = input.userId || context.user.id;
        const response = await AdminService.SendTestPushNotification(input.message, userId);
        return handleResponse(response);
    }),
    resetAllMaps: adminAuth.handler(async () => {
        const response = await AdminService.resetAllUserRoguelikeMaps();
        return handleResponse(response);
    }),
    manualGangPayout: adminAuth.input(adminSchema.manualGangPayout).handler(async ({ input, context }) => {
        const response = await AdminService.ManualGangPayout(input.gangId, input.amount, context.user.id);
        return handleResponse(response);
    }),
    sendAnnouncementMessage: adminAuth.input(adminSchema.sendAnnouncementMessage).handler(async ({ input }) => {
        const response = await AdminService.CreateAnnouncementMessage(input.message);
        return handleResponse(response);
    }),
    toggleMaintenance: adminAuth.input(adminSchema.toggleMaintenance).handler(async ({ input, context }) => {
        const response = await AdminService.ToggleMaintenanceMode(input.enabled, context.user.id);
        return handleResponse(response);
    }),
};
