import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

/**
 * Hook for updating user profile details
 * @returns {Object} The mutation object with mutate function and other properties
 */
const useUpdateProfile = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        orpc.user.updateProfileDetails.mutationOptions({
            onSuccess: () => {
                toast.success("Changes saved successfully!");
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
            },
            onError: (error) => {
                const errorMessage = error.message || "An unknown error occurred";
                toast.error(errorMessage);
            },
        })
    );

    return mutation;
};

export default useUpdateProfile;
