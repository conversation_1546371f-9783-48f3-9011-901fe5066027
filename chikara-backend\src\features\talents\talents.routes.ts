import { isLoggedInAuth, canMakeStateChangesAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as TalentsController from "./talents.controller.js";
import { equipAbilitySchema, unequipAbilitySchema, unlockTalentSchema } from "./talents.validation.js";

export const talentsRouter = {
    getTalents: isLoggedInAuth.handler(async () => {
        const response = await TalentsController.getTalents();
        return handleResponse(response);
    }),

    getUnlockedTalents: isLoggedInAuth.handler(async ({ context }) => {
        const response = await TalentsController.getUnlockedTalents(context.user.id);
        return handleResponse(response);
    }),

    getEquippedAbilities: isLoggedInAuth.handler(async ({ context }) => {
        const response = await TalentsController.getEquippedAbilities(context.user.id);
        return handleResponse(response);
    }),

    unlockTalent: canMakeStateChangesAuth.input(unlockTalentSchema).handler(async ({ input, context }) => {
        const response = await TalentsController.unlock(context.user.id, input.talentId);
        return handleResponse(response);
    }),

    equipAbility: canMakeStateChangesAuth.input(equipAbilitySchema).handler(async ({ input, context }) => {
        const response = await TalentsController.equipAbility(context.user.id, input.talentId, input.slot);
        return handleResponse(response);
    }),

    unequipAbility: canMakeStateChangesAuth.input(unequipAbilitySchema).handler(async ({ input, context }) => {
        const response = await TalentsController.unequipAbility(context.user.id, input.slot);
        return handleResponse(response);
    }),

    resetTalents: canMakeStateChangesAuth.handler(async ({ context }) => {
        const response = await TalentsController.resetTalents(context.user.id);
        return handleResponse(response);
    }),
};

export default talentsRouter;
