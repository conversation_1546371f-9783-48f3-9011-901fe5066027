import { isLoggedInAuth, canMakeStateChang<PERSON>Auth, adminAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as MiningController from "./mining.controller.js";
import * as ScavengingController from "./scavenging/scavenging.controller.js";
import skillsSchema from "./skills.validation.js";

export const skillsRouter = {
    // Mining routes
    startMining: canMakeStateChangesAuth.input(skillsSchema.startMining).handler(async ({ input, context }) => {
        const response = await MiningController.startMining(context.user.id, input.difficulty);
        return handleResponse(response);
    }),

    processSwing: canMakeStateChangesAuth.input(skillsSchema.processSwing).handler(async ({ input, context }) => {
        const response = await MiningController.processSwing(context.user.id, input.hitPosition, input.targetPosition);
        return handleResponse(response);
    }),

    getMiningSession: isLoggedInAuth.handler(async ({ context }) => {
        const response = await MiningController.getMiningSession(context.user.id);
        return handleResponse(response);
    }),

    cancelMining: canMakeStateChangesAuth.handler(async ({ context }) => {
        const response = await MiningController.cancelMining(context.user.id);
        return handleResponse(response);
    }),

    // Scavenging routes
    scavenging: {
        generateGrid: canMakeStateChangesAuth
            .input(skillsSchema.generateScavengingGrid)
            .handler(async ({ input, context }) => {
                const response = await ScavengingController.generateGrid(context.user.id, input.difficultyTier);
                return handleResponse(response);
            }),

        revealCell: canMakeStateChangesAuth.input(skillsSchema.revealCell).handler(async ({ input, context }) => {
            const { sessionId, row, col } = input;
            const response = await ScavengingController.revealCell(context.user.id, sessionId, row, col);
            return handleResponse(response);
        }),

        getActiveSession: isLoggedInAuth.handler(async ({ context }) => {
            const response = await ScavengingController.getActiveSession(context.user.id);
            return handleResponse(response);
        }),

        endSession: canMakeStateChangesAuth.input(skillsSchema.endSession).handler(async ({ input, context }) => {
            const { sessionId } = input;
            const response = await ScavengingController.endSession(context.user.id, sessionId);
            return handleResponse(response);
        }),

        resetGrid: adminAuth.input(skillsSchema.resetGrid).handler(async ({ input, context }) => {
            const { sessionId } = input;
            const response = await ScavengingController.resetGrid(context.user.id, sessionId);
            return handleResponse(response);
        }),

        devGrid: adminAuth.input(skillsSchema.devGrid).handler(async ({ input }) => {
            const { sessionId } = input;
            const response = await ScavengingController.displayDevGrid(sessionId);
            return handleResponse(response);
        }),
    },
};
