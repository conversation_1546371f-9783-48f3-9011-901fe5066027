import { useMutation, useQueryClient } from "@tanstack/react-query";
import { orpc } from "@/lib/orpc";
import { APIROUTES } from "@/helpers/apiRoutes";

export interface TrainingResult {
    statProgress?: {
        leveledUp: boolean;
        currentLevel: number;
        levelsGained: number;
        expGained: number;
        statName: string;
    };
    focusRemaining?: number;
    dailyFatigueRemaining?: number;
}

interface UseTrainStatProps {
    onSuccess?: (_result: TrainingResult, _statName: string) => void;
    onError?: (_error: string) => void;
}

const useTrainStat = ({ onSuccess, onError }: UseTrainStatProps = {}) => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.user.train.mutationOptions({
            onSuccess: (response, variables) => {
                const { statProgress, focusRemaining, dailyFatigueRemaining } = response;

                // Add statName to progress if it exists
                const result: TrainingResult = {
                    statProgress: statProgress
                        ? {
                              ...statProgress,
                              statName: variables.stat,
                          }
                        : undefined,
                    focusRemaining,
                    dailyFatigueRemaining,
                };

                // Call the success callback
                onSuccess?.(result, variables.stat);

                // Invalidate queries to refresh user data
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
            },
            onError: (error) => {
                const errorMessage = error.message || "Unknown error occurred";
                console.error("Training error:", errorMessage);
                onError?.(errorMessage);
            },
        })
    );
};

export default useTrainStat;
