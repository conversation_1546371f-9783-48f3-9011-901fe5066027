{"version": "3.2.4", "results": [[":src/features/bank/__tests__/bank.controller.test.ts", {"duration": 103.**************, "failed": false}], [":src/core/__tests__/statuseffect.service.test.ts", {"duration": 36.**************, "failed": false}], [":src/features/social/__tests__/social.service.test.ts", {"duration": 263.*************, "failed": false}], [":src/features/battle/__tests__/battle.controller.test.ts", {"duration": 109.**************, "failed": false}], [":src/features/auction/__tests__/auction.repository.test.ts", {"duration": 24.**************, "failed": false}], [":src/lib/__tests__/cacheUtility.test.ts", {"duration": 30.***************, "failed": false}], [":src/features/auction/__tests__/auction.integration.test.ts", {"duration": 21.**************, "failed": false}], [":src/features/job/__tests__/job.controller.test.ts", {"duration": 96.**************, "failed": false}], [":src/features/mission/__tests__/mission.controller.test.ts", {"duration": 120.*************, "failed": false}], [":src/features/social/__tests__/social.repository.test.ts", {"duration": 40.01870000000008, "failed": false}], [":src/features/job/__tests__/job.repository.test.ts", {"duration": 23.800399999999968, "failed": false}], [":src/core/__tests__/loot.service.test.ts", {"duration": 24.622799999999984, "failed": false}], [":src/lib/prisma/__tests__/itemExtensions.test.ts", {"duration": 16.833999999999946, "failed": false}], [":src/features/mission/__tests__/mission.routes.test.ts", {"duration": 116.89649999999983, "failed": false}], [":src/features/job/__tests__/job.helpers.test.ts", {"duration": 140.71090000000004, "failed": false}], [":src/features/battle/__tests__/battleAI/battle.ai.test.ts", {"duration": 52.2471000000005, "failed": false}], [":src/features/skills/scavenging/__tests__/scavenging.helpers.test.ts", {"duration": 64.67600000000016, "failed": false}], [":src/features/social/__tests__/social.validation.test.ts", {"duration": 30.01************, "failed": false}], [":src/features/mission/__tests__/mission.repository.test.ts", {"duration": 25.***************, "failed": false}], [":src/features/auction/__tests__/auction.utils.test.ts", {"duration": 60.**************, "failed": false}], [":src/features/battle/helpers/__tests__/battle.scaling.test.ts", {"duration": 21.***************, "failed": false}], [":src/features/battle/__tests__/battleAI/buffAbilityScores.test.ts", {"duration": 13.**************, "failed": false}], [":src/features/bank/__tests__/bank.repository.test.ts", {"duration": 10.**************, "failed": false}], [":src/features/skills/scavenging/__tests__/scavenging.state.test.ts", {"duration": 16.***************, "failed": false}], [":src/features/explore/__tests__/explore.controller.test.ts", {"duration": 60.***************, "failed": false}], [":src/middleware/__tests__/validate.test.ts", {"duration": 22.**************, "failed": false}], [":src/lib/__tests__/talentCache.test.ts", {"duration": 11.***************, "failed": false}], [":src/utils/__tests__/routeHandler.test.ts", {"duration": 13.***************, "failed": false}], [":src/middleware/__tests__/errorHandler.test.ts", {"duration": 11.**************, "failed": false}], [":src/features/shrine/__tests__/shrine.validation.test.ts", {"duration": 13.**************, "failed": false}], [":src/__tests__/prismaClientMock.test.ts", {"duration": 4.***************, "failed": false}], [":src/features/explore/__tests__/explore.grid.test.ts", {"duration": 94.************19, "failed": false}], [":src/features/user/__tests__/user.shield-offhand.test.ts", {"duration": 16.511899999999514, "failed": false}], [":src/features/explore/__tests__/explore.repository.test.ts", {"duration": 30.729400000000055, "failed": false}], [":src/features/explore/__tests__/explore.helpers.test.ts", {"duration": 20.934799999999996, "failed": false}], [":src/features/actionlog/__tests__/actionlog.orpc.test.ts", {"duration": 14.96329999999989, "failed": false}]]}