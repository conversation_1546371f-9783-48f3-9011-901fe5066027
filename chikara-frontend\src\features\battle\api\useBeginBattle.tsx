import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useBeginBattle = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.battle.begin.mutationOptions({
            onSuccess: () => {
                // Fetch latest battle state and refresh user info
                queryClient.invalidateQueries({ queryKey: orpc.battle.getStatus.key() });
                queryClient.invalidateQueries({ queryKey: APIROUTES.USER.CURRENTUSERINFO });
            },
        })
    );
};

export default useBeginBattle;
