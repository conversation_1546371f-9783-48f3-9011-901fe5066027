import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc, type AppRouterClient } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export type BattleAttackResponse = Awaited<ReturnType<AppRouterClient["battle"]["attack"]>>;

export const useStartAttack = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.battle.attack.mutationOptions({
            // Don't invalidate battle status to prevent UI from resetting
            onSuccess: (data) => {
                if (data.battleState.state === "finished") {
                    queryClient.invalidateQueries({ queryKey: APIROUTES.USER.CURRENTUSERINFO });
                }
            },
        })
    );
};

export default useStartAttack;
