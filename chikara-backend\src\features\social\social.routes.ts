import { handleResponse } from "../../utils/routeHandler.js";
import { isLoggedInAuth } from "../../lib/orpc.js";
import * as SocialController from "./social.controller.js";
import { socialSchema } from "./social.validation.js";

export const socialRouter = {
    // Friend routes
    getFriends: isLoggedInAuth.handler(async ({ context }) => {
        const response = await SocialController.getFriendsList(context.user.id);
        return handleResponse(response);
    }),

    getFriendRequests: isLoggedInAuth.handler(async ({ context }) => {
        const response = await SocialController.getFriendRequests(context.user.id);
        return handleResponse(response);
    }),

    sendFriendRequest: isLoggedInAuth.input(socialSchema.sendFriendRequest).handler(async ({ input, context }) => {
        const response = await SocialController.sendFriendRequest(context.user.id, input.userId);
        return handleResponse(response);
    }),

    respondToFriendRequest: isLoggedInAuth
        .input(socialSchema.respondToFriendRequest)
        .handler(async ({ input, context }) => {
            const response = await SocialController.respondToFriendRequest(
                context.user.id,
                input.requestId,
                input.accept
            );
            return handleResponse(response);
        }),

    removeFriend: isLoggedInAuth.input(socialSchema.removeFriend).handler(async ({ input, context }) => {
        const response = await SocialController.removeFriend(context.user.id, input.friendId);
        return handleResponse(response);
    }),

    updateFriendNote: isLoggedInAuth.input(socialSchema.updateFriendNote).handler(async ({ input, context }) => {
        const response = await SocialController.updateFriendNote(context.user.id, input.friendId, input.note ?? null);
        return handleResponse(response);
    }),

    // User status and privacy routes
    updateStatusMessage: isLoggedInAuth.input(socialSchema.updateStatusMessage).handler(async ({ input, context }) => {
        const response = await SocialController.updateStatusMessage(context.user.id, input.message ?? null);
        return handleResponse(response);
    }),

    updatePrivacySettings: isLoggedInAuth
        .input(socialSchema.togglePrivacySetting)
        .handler(async ({ input, context }) => {
            const response = await SocialController.updatePrivacySettings(context.user.id, input.showLastOnline);
            return handleResponse(response);
        }),

    // Rival routes
    getRivals: isLoggedInAuth.handler(async ({ context }) => {
        const response = await SocialController.getRivalsList(context.user.id);
        return handleResponse(response);
    }),

    addRival: isLoggedInAuth.input(socialSchema.addRival).handler(async ({ input, context }) => {
        const response = await SocialController.addRival(context.user.id, input.userId);
        return handleResponse(response);
    }),

    removeRival: isLoggedInAuth.input(socialSchema.removeRival).handler(async ({ input, context }) => {
        const response = await SocialController.removeRival(context.user.id, input.rivalId);
        return handleResponse(response);
    }),

    updateRivalNote: isLoggedInAuth.input(socialSchema.updateRivalNote).handler(async ({ input, context }) => {
        const response = await SocialController.updateRivalNote(context.user.id, input.rivalId, input.note ?? null);
        return handleResponse(response);
    }),
};
