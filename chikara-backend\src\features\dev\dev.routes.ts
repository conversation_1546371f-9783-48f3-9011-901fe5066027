import * as <PERSON><PERSON>ontroller from "./dev.controller.js";
import * as DevRepository from "../../repositories/dev.repository.js";
import * as RoguelikeController from "../roguelike/roguelike.controller.js";
import { devEnvAuth, adminAuth, isLoggedInAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import devSchema from "./dev.validation.js";
import type { ENCOUNTER_LOCATIONS } from "../roguelike/roguelike.constants.js";

export const devRouter = {
    // Test notification endpoint (GET converted to query with input)
    notify: isLoggedInAuth.input(devSchema.sendNotification).handler(async ({ input, context }) => {
        const response = await DevController.SendNotification(context.user.id, input.type, input.details);
        return handleResponse(response);
    }),

    // Admin test endpoints
    testEmail: adminAuth.handler(async () => {
        const response = await DevController.TestEmail();
        return handleResponse(response);
    }),

    sendAiChat: adminAuth.handler(async () => {
        const response = await DevController.SendAIChatMessage();
        return handleResponse(response);
    }),

    // Dev environment endpoints
    addXp: devEnvAuth.input(devSchema.addXp).handler(async ({ input, context }) => {
        const response = await DevController.AddXp(context.user.id, input.xp);
        return handleResponse(response);
    }),

    fullHeal: devEnvAuth.handler(async ({ context }) => {
        const response = await DevController.FullHeal(context.user.id);
        return handleResponse(response);
    }),

    fullHealAll: devEnvAuth.handler(async () => {
        const response = await DevController.FullHealEveryone();
        return handleResponse(response);
    }),

    startPvpBattle: devEnvAuth.handler(async ({ context }) => {
        const response = await DevController.StartRandomPVPBattle(context.user.id);
        return handleResponse(response);
    }),

    resetQuests: devEnvAuth.handler(async ({ context }) => {
        const response = await DevController.ResetQuests(context.user.id);
        return handleResponse(response);
    }),

    randomRoguelike: devEnvAuth.handler(async ({ context }) => {
        const result = await DevController.StartRandomMap(context.user.id);

        // Special case for roguelike which needs to call another controller
        if (result.data) {
            const { level, location } = result.data;
            const roguelikeResponse = await RoguelikeController.newRun(
                context.user.id,
                level,
                location as ENCOUNTER_LOCATIONS
            );
            return handleResponse(roguelikeResponse);
        }

        return handleResponse(result);
    }),

    addCash: devEnvAuth.input(devSchema.addCash).handler(async ({ input, context }) => {
        const response = await DevController.AddCash(context.user.id, input.cash);
        return handleResponse(response);
    }),

    addStats: devEnvAuth.input(devSchema.addStats).handler(async ({ input, context }) => {
        const response = await DevController.AddStats(context.user.id, input.amount);
        return handleResponse(response);
    }),

    removeStats: devEnvAuth.input(devSchema.removeStats).handler(async ({ input, context }) => {
        const response = await DevController.RemoveStats(context.user.id, input.amount);
        return handleResponse(response);
    }),

    addAllItems: devEnvAuth.handler(async ({ context }) => {
        const response = await DevController.AddAllItems(context.user.id);
        return handleResponse(response);
    }),

    completeQuests: devEnvAuth.handler(async ({ context }) => {
        const response = await DevController.CompleteAllQuests(context.user.id);
        return handleResponse(response);
    }),

    resetBattles: devEnvAuth.handler(async () => {
        const response = await DevController.cleanupAllBattles();
        return handleResponse(response);
    }),

    removeAllEffects: devEnvAuth.input(devSchema.removeAllEffects).handler(async ({ input, context }) => {
        const selectedUser = input.userId || context.user.id;
        const response = await DevController.RemoveAllEffects(selectedUser);
        return handleResponse(response);
    }),

    addRandomEffects: devEnvAuth.input(devSchema.addRandomEffects).handler(async ({ input, context }) => {
        const selectedUser = input.userId || context.user.id;
        const response = await DevController.AddRandomEffects(selectedUser);
        return handleResponse(response);
    }),

    addItem: devEnvAuth.input(devSchema.addItem).handler(async ({ input, context }) => {
        const response = await DevController.AddItem(context.user.id, input.itemId, input.quantity);
        return handleResponse(response);
    }),

    // Pet endpoints
    getPetsList: devEnvAuth.handler(async () => {
        const petsList = await DevRepository.findPetsList();
        return { data: petsList };
    }),

    hatchEggs: devEnvAuth.handler(async ({ context }) => {
        const response = await DevController.HatchEggs(context.user.id);
        return handleResponse(response);
    }),

    setFullPetHappiness: devEnvAuth.handler(async ({ context }) => {
        const response = await DevController.SetAllPetsHappiness(context.user.id);
        return handleResponse(response);
    }),

    addPetXp: devEnvAuth.input(devSchema.addPetXp).handler(async ({ input, context }) => {
        const response = await DevController.AddXpToAllPets(context.user.id, input.xp);
        return handleResponse(response);
    }),

    deleteExploreNodes: devEnvAuth.handler(async ({ context }) => {
        const response = await DevController.DeleteExploreNodes(context.user.id);
        return handleResponse(response);
    }),
};
