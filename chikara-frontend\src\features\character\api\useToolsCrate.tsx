import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

/**
 * Custom hook to use tools crate
 */
export const useToolsCrate = () => {
    const queryClient = useQueryClient();

    return useMutation(
        // eslint-disable-next-line react-hooks/react-compiler
        orpc.item.useToolsCrate.mutationOptions({
            onSuccess: () => {
                setTimeout(() => {
                    queryClient.invalidateQueries({
                        queryKey: orpc.user.getInventory.key(),
                    });
                    queryClient.invalidateQueries({
                        queryKey: APIROUTES.USER.CURRENTUSERINFO,
                    });
                }, 30);

                toast.success("You redeemed 10 Tools for your gang!");
            },
            onError: (error) => {
                const errorMessage = error.message || "Unknown error occurred";
                console.error(errorMessage);
                toast.error(errorMessage);
            },
        })
    );
};

export default useToolsCrate;
