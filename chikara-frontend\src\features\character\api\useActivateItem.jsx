import { APIROUTES } from "@/helpers/apiRoutes";
import axios from "@/helpers/axiosInstance";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

const useActivateItem = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: async ({ currentUser, item }) => {
            if (item.health > 0) {
                if (currentUser.currentHealth === currentUser.health) {
                    toast.error("You're already full hp!");
                    return;
                }
            }
            if (currentUser.jailedUntil > 0 || currentUser.hospitalisedUntil > 0) {
                toast.error("You can't use this in your current state!");
                return;
            }

            return await axios.post(`${APIROUTES.USER.USEITEM}`, { itemId: item.id });
        },
        onSuccess: async (data) => {
            await queryClient.invalidateQueries({
                queryKey: APIROUTES.USER.INVENTORY,
            });
            await queryClient.invalidateQueries({
                queryKey: APIROUTES.USER.CURRENTUSERINFO,
            });
            console.log(data);
            //   if (item.health > 0) {
            //     toast.success(`You recovered ${item.health} HP!`);
            //   }
            //   if (item.energy > 0) {
            //     toast.success(`You recovered ${item.energy} Energy!`);
            //   }
            //   if (item.actionPoints > 0) {
            //     toast.success(`You recovered ${item.actionPoints} AP!`);
            //   }
        },
        onError: (error) => {
            toast.error(error?.response?.data || "An error occurred");
            //   toast.error(`Error: ${error.message}`);
        },
    });

    return {
        activateItem: mutation,
    };
};

export default useActivateItem;
