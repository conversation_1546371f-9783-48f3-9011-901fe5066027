import * as SocialController from "./social.controller.js";
import socialValidation from "./social.validation.js";
import authHelper from "../../middleware/authMiddleware.js";
import validate from "../../middleware/validate.js";
import { routeHandler } from "../../utils/routeHandler.js";
import { Router } from "express";
const router = Router();
router.get("/friends", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await SocialController.getFriendsList(req.user.id);
}));
router.get("/friends/requests", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await SocialController.getFriendRequests(req.user.id);
}));
router.post("/friends/request", authHelper.IsLoggedIn, validate(socialValidation.sendFriendRequestSchema), routeHandler(async (req) => {
    return await SocialController.sendFriendRequest(req.user.id, req.body.userId);
}));
router.post("/friends/request/respond", authHelper.IsLoggedIn, validate(socialValidation.respondToFriendRequestSchema), routeHandler(async (req) => {
    return await SocialController.respondToFriendRequest(req.user.id, req.body.requestId, req.body.accept);
}));
router.delete("/friends/:friendId", authHelper.IsLoggedIn, validate(socialValidation.friendIdParamSchema, "params"), routeHandler(async (req) => {
    return await SocialController.removeFriend(req.user.id, Number.parseInt(req.params.friendId, 10));
}));
router.post("/friends/note", authHelper.IsLoggedIn, validate(socialValidation.updateFriendNoteSchema), routeHandler(async (req) => {
    return await SocialController.updateFriendNote(req.user.id, req.body.friendId, req.body.note);
}));
router.post("/status-message", authHelper.IsLoggedIn, validate(socialValidation.updateStatusMessageSchema), routeHandler(async (req) => {
    return await SocialController.updateStatusMessage(req.user.id, req.body.message);
}));
router.post("/privacy", authHelper.IsLoggedIn, validate(socialValidation.togglePrivacySettingSchema), routeHandler(async (req) => {
    return await SocialController.updatePrivacySettings(req.user.id, req.body.showLastOnline);
}));
router.get("/rivals", authHelper.IsLoggedIn, routeHandler(async (req) => {
    return await SocialController.getRivalsList(req.user.id);
}));
router.post("/rivals/add", authHelper.IsLoggedIn, validate(socialValidation.addRivalSchema), routeHandler(async (req) => {
    return await SocialController.addRival(req.user.id, req.body.userId);
}));
router.delete("/rivals/:rivalId", authHelper.IsLoggedIn, validate(socialValidation.rivalIdParamSchema, "params"), routeHandler(async (req) => {
    return await SocialController.removeRival(req.user.id, Number.parseInt(req.params.rivalId, 10));
}));
router.post("/rivals/note", authHelper.IsLoggedIn, validate(socialValidation.updateRivalNoteSchema), routeHandler(async (req) => {
    return await SocialController.updateRivalNote(req.user.id, req.body.rivalId, req.body.note);
}));
export default router;
