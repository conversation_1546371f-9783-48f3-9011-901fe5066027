import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { toast } from "react-hot-toast";

const useSetNotificationSettings = (initialSettings) => {
    const [pushNotificationsEnabled, setPushNotificationsEnabled] = useState(initialSettings?.pushNotificationsEnabled);
    const queryClient = useQueryClient();

    const mutation = useMutation(
        orpc.notification.updatePushSettings.mutationOptions({
            onSuccess: () => {
                toast.success(`Saved Successfully`);
                queryClient.invalidateQueries({
                    queryKey: APIROUTES.USER.CURRENTUSERINFO,
                });
            },
            onError: (error) => {
                toast.error(`Error: ${error.message}`);
            },
        })
    );

    return {
        pushNotificationsEnabled,
        setPushNotificationsEnabled,
        saveNotificationSettings: () => mutation.mutate({ pushEnabled: pushNotificationsEnabled }),
    };
};

export default useSetNotificationSettings;
