import { APIROUTES } from "@/helpers/apiRoutes";
import { orpc } from "@/lib/orpc";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const usePostBattleAction = () => {
    const queryClient = useQueryClient();

    return useMutation(
        orpc.battle.postBattleAction.mutationOptions({
            onSuccess: () => {
                // Invalidate battle status and user info to refresh UI
                queryClient.invalidateQueries({ queryKey: orpc.battle.getStatus.key() });
                queryClient.invalidateQueries({ queryKey: APIROUTES.USER.CURRENTUSERINFO });
            },
        })
    );
};

export default usePostBattleAction;
